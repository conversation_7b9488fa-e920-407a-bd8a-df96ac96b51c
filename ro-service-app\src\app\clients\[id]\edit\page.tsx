'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Save, AlertTriangle } from 'lucide-react';
import { useNotification } from '@/contexts/NotificationContext';

interface FormData {
  name: string;
  phone: string;
  location: string;
  notes: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval: number;
  scheduledDate: string;
}

interface FormErrors {
  name?: string;
  phone?: string;
  location?: string;
  serviceInterval?: string;
  scheduledDate?: string;
}

const EditClientPage = () => {
  const router = useRouter();
  const params = useParams();
  const clientId = params.id as string;
  const { checkDailyNotification } = useNotification();
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    phone: '+91',
    location: '',
    notes: '',
    serviceType: 'recurring',
    serviceInterval: 3,
    scheduledDate: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const serviceIntervalOptions = [
    { value: 1, label: '1 month' },
    { value: 2, label: '2 months' },
    { value: 3, label: '3 months' },
    { value: 4, label: '4 months' },
    { value: 5, label: '5 months' },
    { value: 6, label: '6 months' },
    { value: 12, label: '1 year' }
  ];

  // Fetch client data
  useEffect(() => {
    const fetchClient = async () => {
      try {
        const response = await fetch(`/api/clients/${clientId}`);
        const result = await response.json();
        
        if (result.success) {
          const client = result.data;
          setFormData({
            name: client.name || '',
            phone: client.phone || '+91',
            location: client.location || '',
            notes: client.notes || '',
            serviceType: client.serviceType || 'recurring',
            serviceInterval: client.serviceInterval || 3,
            scheduledDate: client.scheduledDate ? new Date(client.scheduledDate).toISOString().split('T')[0] : ''
          });
        } else {
          setSubmitError('Failed to load client data');
        }
      } catch (error) {
        setSubmitError('Error loading client data');
      } finally {
        setFetchLoading(false);
      }
    };

    if (clientId) {
      fetchClient();
    }
  }, [clientId]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Client name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters long';
    }

    // Phone validation
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^\+91[6-9]\d{9}$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid Indian phone number (e.g., +************)';
    }

    // Location validation
    if (!formData.location.trim()) {
      newErrors.location = 'Service location is required';
    } else if (formData.location.trim().length < 5) {
      newErrors.location = 'Location must be at least 5 characters long';
    }

    // Service type specific validation
    if (formData.serviceType === 'recurring') {
      if (!serviceIntervalOptions.find(option => option.value === formData.serviceInterval)) {
        newErrors.serviceInterval = 'Please select a valid service interval';
      }
    } else if (formData.serviceType === 'scheduled') {
      if (!formData.scheduledDate) {
        newErrors.scheduledDate = 'Scheduled date is required';
      } else {
        const selectedDate = new Date(formData.scheduledDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (selectedDate < today) {
          newErrors.scheduledDate = 'Scheduled date cannot be in the past';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'phone') {
      // Ensure phone always starts with +91
      let phoneValue = value;
      if (!phoneValue.startsWith('+91')) {
        phoneValue = '+91' + phoneValue.replace(/^\+?91?/, '');
      }
      // Limit to 13 characters (+91 + 10 digits)
      if (phoneValue.length <= 13) {
        setFormData(prev => ({ ...prev, [name]: phoneValue }));
      }
    } else if (name === 'serviceInterval') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) }));
    } else if (name === 'serviceType') {
      // Reset related fields when service type changes
      setFormData(prev => ({ 
        ...prev, 
        [name]: value,
        serviceInterval: value === 'recurring' ? 3 : prev.serviceInterval,
        scheduledDate: value === 'scheduled' ? prev.scheduledDate : ''
      }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error for this field when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setSubmitError(null);

    try {
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        // Trigger notification check for updated scheduled services
        checkDailyNotification();
        router.push('/clients');
      } else {
        setSubmitError(result.error || 'Failed to update client');
      }
    } catch (error) {
      setSubmitError('An error occurred while updating the client');
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading client data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link 
                href="/clients"
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">Edit Client</h1>
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          {submitError && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{submitError}</p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Client Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Client Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter client's full name"
              />
              {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
            </div>

            {/* Phone Number */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number *
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.phone ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="+************"
              />
              {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
              <p className="mt-1 text-sm text-gray-500">Format: +91 followed by 10 digits</p>
            </div>

            {/* Service Location */}
            <div>
              <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                Service Location *
              </label>
              <textarea
                id="location"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.location ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter complete address where RO service will be provided"
              />
              {errors.location && <p className="mt-1 text-sm text-red-600">{errors.location}</p>}
            </div>

            {/* Service Type */}
            <div>
              <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700 mb-2">
                Service Type *
              </label>
              <select
                id="serviceType"
                name="serviceType"
                value={formData.serviceType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="recurring">Recurring Service</option>
                <option value="scheduled">Scheduled Service</option>
              </select>
              <p className="mt-1 text-sm text-gray-500">
                Choose recurring for regular intervals or scheduled for a specific date
              </p>
            </div>

            {/* Service Interval (for recurring services) */}
            {formData.serviceType === 'recurring' && (
              <div>
                <label htmlFor="serviceInterval" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Interval *
                </label>
                <select
                  id="serviceInterval"
                  name="serviceInterval"
                  value={formData.serviceInterval}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.serviceInterval ? 'border-red-300' : 'border-gray-300'
                  }`}
                >
                  {serviceIntervalOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.serviceInterval && <p className="mt-1 text-sm text-red-600">{errors.serviceInterval}</p>}
                <p className="mt-1 text-sm text-gray-500">How often should the RO be serviced?</p>
              </div>
            )}

            {/* Scheduled Date (for scheduled services) */}
            {formData.serviceType === 'scheduled' && (
              <div>
                <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Scheduled Service Date *
                </label>
                <input
                  type="date"
                  id="scheduledDate"
                  name="scheduledDate"
                  value={formData.scheduledDate}
                  onChange={handleInputChange}
                  min={new Date().toISOString().split('T')[0]}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.scheduledDate ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
                {errors.scheduledDate && <p className="mt-1 text-sm text-red-600">{errors.scheduledDate}</p>}
                <p className="mt-1 text-sm text-gray-500">Select the specific date for the next service</p>
              </div>
            )}

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Any additional information about the client or service requirements..."
              />
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transition-colors"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Updating...</span>
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    <span>Update Client</span>
                  </>
                )}
              </button>

              <Link
                href="/clients"
                className="flex-1 sm:flex-none bg-gray-100 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 flex items-center justify-center transition-colors"
              >
                Cancel
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditClientPage;
