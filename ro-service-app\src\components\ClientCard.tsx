'use client';

import { useState } from 'react';
import { Phone, MapPin, Calendar, CheckCircle, Clock } from 'lucide-react';
import { formatDate, formatPhoneNumber, getServiceStatus, getStatusColor } from '@/lib/utils';

interface ClientCardProps {
  client: {
    _id: string;
    name: string;
    phone: string;
    location: string;
    notes?: string;
    serviceType: 'recurring' | 'scheduled';
    serviceInterval?: number;
    scheduledDate?: string;
    nextServiceDate: string;
  };
  onServiceComplete?: (clientId: string) => void;
  priority?: 'high' | 'medium' | 'low';
  showCompleteButton?: boolean;
}

const ClientCard = ({ 
  client, 
  onServiceComplete, 
  priority = 'low',
  showCompleteButton = true 
}: ClientCardProps) => {
  const [completing, setCompleting] = useState(false);
  const serviceStatus = getServiceStatus(new Date(client.nextServiceDate));
  const statusColor = getStatusColor(serviceStatus);

  const handleCompleteService = async () => {
    if (!onServiceComplete) return;
    
    try {
      setCompleting(true);
      
      // Create a service record and mark it as completed
      const serviceResponse = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: client._id,
          serviceDate: new Date().toISOString(),
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!serviceResponse.ok) {
        throw new Error('Failed to create service record');
      }

      const serviceResult = await serviceResponse.json();
      
      // Complete the service
      const completeResponse = await fetch(`/api/services/${serviceResult.data._id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!completeResponse.ok) {
        throw new Error('Failed to complete service');
      }

      onServiceComplete(client._id);
    } catch (error) {
      console.error('Error completing service:', error);
      alert('Failed to complete service. Please try again.');
    } finally {
      setCompleting(false);
    }
  };

  const getPriorityBorder = () => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-l-red-500';
      case 'medium':
        return 'border-l-4 border-l-orange-500';
      default:
        return 'border-l-4 border-l-blue-500';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow ${getPriorityBorder()}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{client.name}</h3>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusColor}`}>
              <Clock className="w-3 h-3 mr-1" />
              {serviceStatus === 'overdue' && 'Overdue'}
              {serviceStatus === 'today' && 'Due Today'}
              {serviceStatus === 'tomorrow' && 'Due Tomorrow'}
              {serviceStatus === 'upcoming' && 'Upcoming'}
            </div>
          </div>
          {showCompleteButton && (serviceStatus === 'overdue' || serviceStatus === 'today') && (
            <button
              onClick={handleCompleteService}
              disabled={completing}
              className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {completing ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Complete
                </>
              )}
            </button>
          )}
        </div>

        {/* Contact Info */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center text-gray-600">
            <Phone className="w-4 h-4 mr-3 flex-shrink-0" />
            <a 
              href={`tel:${client.phone}`}
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              {formatPhoneNumber(client.phone)}
            </a>
          </div>
          
          <div className="flex items-start text-gray-600">
            <MapPin className="w-4 h-4 mr-3 flex-shrink-0 mt-0.5" />
            <span className="text-sm">{client.location}</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            <Calendar className="w-4 h-4 mr-3 flex-shrink-0" />
            <span className="text-sm">
              {client.serviceType === 'scheduled' ? 'Scheduled Service:' : 'Next Service:'} {formatDate(client.nextServiceDate)}
            </span>
          </div>
        </div>

        {/* Service Type Info */}
        <div className="text-sm text-gray-500 mb-4">
          {client.serviceType === 'scheduled' ? (
            <span>One-time scheduled service</span>
          ) : (
            <span>
              Recurring: {client.serviceInterval} {client.serviceInterval === 1 ? 'month' : 'months'}
            </span>
          )}
        </div>

        {/* Notes */}
        {client.notes && (
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-sm text-gray-600">{client.notes}</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClientCard;
