'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Phone, MapPin, Calendar, CheckCircle, Clock } from 'lucide-react';
import { formatDate, formatPhoneNumber, getServiceStatus, getStatusColor } from '@/lib/utils';
import { useNotification } from '@/contexts/NotificationContext';
import RecurringServiceModal from './RecurringServiceModal';

interface ClientCardProps {
  client: {
    _id: string;
    name: string;
    phone: string;
    location: string;
    notes?: string;
    serviceType: 'recurring' | 'scheduled';
    serviceInterval?: number;
    scheduledDate?: string;
    nextServiceDate: string;
  };
  onServiceComplete?: (clientId: string) => void;
  priority?: 'high' | 'medium' | 'low';
  showCompleteButton?: boolean;
}

const ClientCard = ({
  client,
  onServiceComplete,
  priority = 'low',
  showCompleteButton = true
}: ClientCardProps) => {
  const router = useRouter();
  const { checkDailyNotification } = useNotification();
  const [completing, setCompleting] = useState(false);
  const [showRecurringModal, setShowRecurringModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const serviceStatus = getServiceStatus(new Date(client.nextServiceDate));
  const statusColor = getStatusColor(serviceStatus);

  const handleCompleteService = async () => {
    if (!onServiceComplete) return;

    try {
      setCompleting(true);
      setError(null);

      console.log('ClientCard - Starting service completion for client:', client._id);

      // Create a service record and mark it as completed
      const serviceResponse = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId: client._id,
          serviceDate: new Date().toISOString(),
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!serviceResponse.ok) {
        const errorData = await serviceResponse.json();
        throw new Error(errorData.error || 'Failed to create service record');
      }

      const serviceResult = await serviceResponse.json();
      console.log('ClientCard - Service record created:', serviceResult.data._id);

      // Complete the service
      const completeResponse = await fetch(`/api/services/${serviceResult.data._id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: `Service completed on ${new Date().toLocaleDateString()}`
        }),
      });

      if (!completeResponse.ok) {
        const errorData = await completeResponse.json();
        throw new Error(errorData.error || 'Failed to complete service');
      }

      const completeResult = await completeResponse.json();
      console.log('ClientCard - Service completed successfully:', completeResult);

      // Update notifications
      await checkDailyNotification();

      // Call the parent callback to refresh data
      onServiceComplete(client._id);

      // Show recurring service modal only for scheduled services
      if (client.serviceType === 'scheduled') {
        setShowRecurringModal(true);
      }

    } catch (error) {
      console.error('Error completing service:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to complete service. Please try again.';
      setError(errorMessage);
    } finally {
      setCompleting(false);
    }
  };

  const handleRecurringYes = async (clientId: string) => {
    console.log('ClientCard - Redirecting to edit page for recurring setup:', clientId);
    setShowRecurringModal(false);
    router.push(`/clients/${clientId}/edit`);
  };

  const handleRecurringNo = () => {
    console.log('ClientCard - User declined recurring setup');
    setShowRecurringModal(false);
    // Just close the modal, the parent callback has already refreshed the data
  };

  const getPriorityBorder = () => {
    switch (priority) {
      case 'high':
        return 'border-l-4 border-l-red-500';
      case 'medium':
        return 'border-l-4 border-l-orange-500';
      default:
        return 'border-l-4 border-l-blue-500';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow ${getPriorityBorder()}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{client.name}</h3>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusColor}`}>
              <Clock className="w-3 h-3 mr-1" />
              {serviceStatus === 'overdue' && 'Overdue'}
              {serviceStatus === 'today' && 'Due Today'}
              {serviceStatus === 'tomorrow' && 'Due Tomorrow'}
              {serviceStatus === 'upcoming' && 'Upcoming'}
            </div>
          </div>
          {showCompleteButton && (serviceStatus === 'overdue' || serviceStatus === 'today') && (
            <button
              onClick={handleCompleteService}
              disabled={completing}
              className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {completing ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Complete
                </>
              )}
            </button>
          )}
        </div>

        {/* Contact Info */}
        <div className="space-y-3 mb-4">
          <div className="flex items-center text-gray-600">
            <Phone className="w-4 h-4 mr-3 flex-shrink-0" />
            <a 
              href={`tel:${client.phone}`}
              className="text-blue-600 hover:text-blue-800 transition-colors"
            >
              {formatPhoneNumber(client.phone)}
            </a>
          </div>
          
          <div className="flex items-start text-gray-600">
            <MapPin className="w-4 h-4 mr-3 flex-shrink-0 mt-0.5" />
            <span className="text-sm">{client.location}</span>
          </div>
          
          <div className="flex items-center text-gray-600">
            <Calendar className="w-4 h-4 mr-3 flex-shrink-0" />
            <span className="text-sm">
              {client.serviceType === 'scheduled' ? 'Scheduled Service:' : 'Next Service:'} {formatDate(client.nextServiceDate)}
            </span>
          </div>
        </div>

        {/* Service Type Info */}
        <div className="text-sm text-gray-500 mb-4">
          {client.serviceType === 'scheduled' ? (
            <span>One-time scheduled service</span>
          ) : (
            <span>
              Recurring: {client.serviceInterval} {client.serviceInterval === 1 ? 'month' : 'months'}
            </span>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={() => setError(null)}
              className="text-xs text-red-500 hover:text-red-700 mt-1"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Notes */}
        {client.notes && (
          <div className="bg-gray-50 rounded-md p-3">
            <p className="text-sm text-gray-600">{client.notes}</p>
          </div>
        )}
      </div>

      {/* Recurring Service Modal */}
      <RecurringServiceModal
        isOpen={showRecurringModal}
        clientName={client.name}
        clientId={client._id}
        onClose={() => setShowRecurringModal(false)}
        onYes={handleRecurringYes}
        onNo={handleRecurringNo}
      />
    </div>
  );
};

export default ClientCard;
