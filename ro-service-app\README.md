# RO Service Manager

A comprehensive web application for managing Reverse Osmosis (RO) water service operations. Built with Next.js, MongoDB, and Tailwind CSS for a modern, responsive experience optimized for field technicians.

## Features

### 🏠 Dashboard
- Today's pending services
- Tomorrow's scheduled services
- Overdue service alerts
- Service statistics overview
- Quick service completion

### 👥 Client Management
- Complete client profiles with contact information
- Service location tracking
- Customizable service intervals (1-12 months)
- Search functionality
- Client notes and service history

### 📅 Service Scheduling
- Automatic next service date calculation
- Recurring service management
- Service status tracking (overdue, today, tomorrow, upcoming)
- Service completion workflow

### 📱 Mobile-First Design
- Responsive design for all screen sizes
- Touch-friendly interface for field technicians
- Bottom navigation for mobile devices
- Optimized for Indian market (+91 phone numbers)

## Technology Stack

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS
- **Database**: MongoDB with Mongoose ODM
- **Icons**: Lucide React
- **Date Handling**: date-fns

## Getting Started

### Prerequisites

- Node.js 18+
- MongoDB (local or cloud instance)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ro-service-app
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env.local` file in the root directory:
```env
MONGODB_URI=mongodb://localhost:27017/ro-service-app
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
```

4. Start the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Database Setup

The application will automatically create the necessary collections and indexes when you first run it. Make sure your MongoDB instance is running and accessible via the connection string in your `.env.local` file.

### Collections Created:
- `clients` - Client information and service schedules
- `services` - Service records and completion history

## Usage

### Adding a New Client
1. Navigate to "Add Client" from the navigation
2. Fill in required information:
   - Client name
   - Phone number (with +91 prefix)
   - Service location
   - Service interval
   - Optional notes
3. The system automatically calculates the next service date

### Managing Services
1. View pending services on the Dashboard
2. Complete services directly from client cards
3. Track service history and status
4. Search clients by name, phone, or location

### Service Intervals
Choose from predefined intervals:
- 1, 2, 3, 4, 5, 6 months
- 1 year

## API Endpoints

### Clients
- `GET /api/clients` - List all clients with optional search
- `POST /api/clients` - Create new client
- `GET /api/clients/[id]` - Get client details
- `PUT /api/clients/[id]` - Update client
- `DELETE /api/clients/[id]` - Delete client

### Services
- `GET /api/services` - List services with filters
- `POST /api/services` - Create new service
- `POST /api/services/[id]/complete` - Mark service as completed

### Dashboard
- `GET /api/dashboard` - Get dashboard data with service statistics

## Mobile Optimization

The application is designed mobile-first with:
- Touch-friendly button sizes (minimum 44px)
- Bottom navigation for easy thumb access
- Responsive grid layouts
- Optimized form inputs for mobile keyboards
- Safe area handling for modern devices

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please contact the development team.
