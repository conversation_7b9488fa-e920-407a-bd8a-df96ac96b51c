import mongoose, { Document, Schema } from 'mongoose';

export interface IService extends Document {
  clientId: mongoose.Types.ObjectId;
  serviceDate: Date;
  status: 'pending' | 'completed' | 'cancelled';
  notes?: string;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ServiceSchema = new Schema<IService>({
  clientId: {
    type: Schema.Types.ObjectId,
    ref: 'Client',
    required: [true, 'Client ID is required']
  },
  serviceDate: {
    type: Date,
    required: [true, 'Service date is required']
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'cancelled'],
    default: 'pending'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  completedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
});

// Update completedAt when status changes to completed
ServiceSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
  }
  next();
});

// Index for efficient queries
ServiceSchema.index({ serviceDate: 1, status: 1 });
ServiceSchema.index({ clientId: 1, serviceDate: -1 });

export default mongoose.models.Service || mongoose.model<IService>('Service', ServiceSchema);
