'use client';

import { useState, useEffect } from 'react';
import { Calendar, Clock, AlertTriangle, Users, CheckCircle } from 'lucide-react';
import { formatDate, getServiceStatus, getStatusColor } from '@/lib/utils';
import ClientCard from './ClientCard';

interface Client {
  _id: string;
  name: string;
  phone: string;
  location: string;
  notes?: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval?: number;
  scheduledDate?: string;
  nextServiceDate: string;
}

interface DashboardData {
  today: Client[];
  tomorrow: Client[];
  overdue: Client[];
  upcoming: Client[];
  stats: {
    totalClients: number;
    todayCount: number;
    tomorrowCount: number;
    overdueCount: number;
    upcomingCount: number;
  };
}

const Dashboard = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(fetchDashboardData, 30000);

    // Refresh when window gains focus (user returns to tab)
    const handleFocus = () => fetchDashboardData();
    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      console.log('Dashboard - fetching data...');
      const response = await fetch('/api/dashboard');
      const result = await response.json();

      console.log('Dashboard - API response:', result);

      if (result.success) {
        setData(result.data);
        console.log('Dashboard - data set successfully:', {
          todayCount: result.data.today.length,
          tomorrowCount: result.data.tomorrow.length,
          overdueCount: result.data.overdue.length,
          upcomingCount: result.data.upcoming.length
        });
      } else {
        setError(result.error || 'Failed to fetch dashboard data');
        console.error('Dashboard - API error:', result.error);
      }
    } catch (err) {
      setError('Failed to fetch dashboard data');
      console.error('Dashboard fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceComplete = async (clientId: string) => {
    console.log('Dashboard - Service completed for client:', clientId);

    // Refresh dashboard data after service completion
    try {
      await fetchDashboardData();
      console.log('Dashboard - Data refreshed successfully after service completion');
    } catch (error) {
      console.error('Dashboard - Error refreshing data after service completion:', error);
      // Still try to refresh after a short delay
      setTimeout(() => {
        fetchDashboardData();
      }, 2000);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchDashboardData}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!data) return null;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">Manage your RO service schedule</p>
      </div>

      {/* Today's Tasks - Prominent Section */}
      {data.today.length > 0 && (
        <div className="mb-8">
          <div className="bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg p-6 text-white">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <AlertTriangle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold">Today's Priority Tasks</h2>
                  <p className="text-orange-100">
                    {data.today.length} service{data.today.length !== 1 ? 's' : ''} scheduled for today
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">{data.today.length}</div>
                <div className="text-sm text-orange-100">Pending</div>
              </div>
            </div>

            <div className="grid gap-3">
              {data.today.slice(0, 3).map((client) => (
                <div key={client._id} className="bg-white bg-opacity-10 rounded-lg p-4 backdrop-blur-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-white">{client.name}</h3>
                      <p className="text-sm text-orange-100">{client.phone}</p>
                      <p className="text-sm text-orange-100 truncate">{client.location}</p>
                    </div>
                    <div className="text-right">
                      <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                      <div className="text-xs text-orange-100 mt-1">Urgent</div>
                    </div>
                  </div>
                </div>
              ))}
              {data.today.length > 3 && (
                <div className="text-center text-orange-100 text-sm">
                  +{data.today.length - 3} more tasks for today
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <Users className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Clients</p>
              <p className="text-2xl font-bold text-gray-900">{data.stats.totalClients}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <AlertTriangle className="w-8 h-8 text-red-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{data.stats.overdueCount}</p>
            </div>
          </div>
        </div>
        
        <div className={`p-4 rounded-lg shadow-sm border ${
          data.stats.todayCount > 0
            ? 'bg-gradient-to-r from-orange-50 to-red-50 border-orange-200'
            : 'bg-white'
        }`}>
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              data.stats.todayCount > 0
                ? 'bg-orange-500 text-white'
                : 'text-orange-600'
            }`}>
              <Clock className="w-5 h-5" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Today's Tasks</p>
              <div className="flex items-center space-x-2">
                <p className={`text-2xl font-bold ${
                  data.stats.todayCount > 0 ? 'text-orange-600' : 'text-gray-900'
                }`}>
                  {data.stats.todayCount}
                </p>
                {data.stats.todayCount > 0 && (
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <Calendar className="w-8 h-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Tomorrow</p>
              <p className="text-2xl font-bold text-blue-600">{data.stats.tomorrowCount}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Upcoming</p>
              <p className="text-2xl font-bold text-green-600">{data.stats.upcomingCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Service Sections */}
      <div className="space-y-8">
        {/* Overdue Services */}
        {data.overdue.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-red-600 mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Overdue Services ({data.overdue.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.overdue.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="high"
                />
              ))}
            </div>
          </section>
        )}

        {/* Today's Services */}
        {data.today.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-orange-600 mb-4 flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Today's Services ({data.today.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.today.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="medium"
                />
              ))}
            </div>
          </section>
        )}

        {/* Tomorrow's Services */}
        {data.tomorrow.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-blue-600 mb-4 flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Tomorrow's Services ({data.tomorrow.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.tomorrow.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="low"
                />
              ))}
            </div>
          </section>
        )}

        {/* Upcoming Services */}
        {data.upcoming.length > 0 && (
          <section>
            <h2 className="text-xl font-semibold text-green-600 mb-4 flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Upcoming Services (Next 7 Days) ({data.upcoming.length})
            </h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.upcoming.map((client) => (
                <ClientCard
                  key={client._id}
                  client={client}
                  onServiceComplete={handleServiceComplete}
                  priority="low"
                />
              ))}
            </div>
          </section>
        )}

        {/* Empty State */}
        {data.overdue.length === 0 && data.today.length === 0 && data.tomorrow.length === 0 && data.upcoming.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Services Scheduled</h3>
            <p className="text-gray-600 mb-6">You don&apos;t have any services scheduled for the next week.</p>
            <a
              href="/clients/new"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Add Your First Client
            </a>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
