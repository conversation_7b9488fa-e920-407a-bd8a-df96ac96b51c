// Test script to verify service completion workflow
// Run this with: node test-service-completion.js

const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ro-service-app';

// Import models (simplified versions for testing)
const ClientSchema = new mongoose.Schema({
  name: { type: String, required: true },
  phone: { type: String, required: true },
  location: { type: String, required: true },
  notes: String,
  serviceType: { type: String, enum: ['recurring', 'scheduled'], default: 'recurring' },
  serviceInterval: { type: Number, enum: [1, 2, 3, 4, 5, 6, 12], default: 3 },
  scheduledDate: Date,
  lastServiceDate: { type: Date, default: null },
  nextServiceDate: { type: Date, required: true }
}, { timestamps: true });

const ServiceSchema = new mongoose.Schema({
  clientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Client', required: true },
  serviceDate: { type: Date, required: true },
  status: { type: String, enum: ['pending', 'completed', 'cancelled'], default: 'pending' },
  notes: String,
  completedAt: Date
}, { timestamps: true });

// Pre-validate hook for Client
ClientSchema.pre('validate', function(next) {
  if (this.serviceType === 'scheduled') {
    if (this.scheduledDate) {
      const schedDate = new Date(this.scheduledDate);
      this.nextServiceDate = new Date(Date.UTC(schedDate.getUTCFullYear(), schedDate.getUTCMonth(), schedDate.getUTCDate()));
    }
  } else {
    const baseDate = this.lastServiceDate || new Date();
    const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));
    const interval = this.serviceInterval || 3;
    nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);
    this.nextServiceDate = nextDate;
  }
  next();
});

// Pre-save hook for Service
ServiceSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'completed' && !this.completedAt) {
    this.completedAt = new Date();
  }
  next();
});

const Client = mongoose.model('Client', ClientSchema);
const Service = mongoose.model('Service', ServiceSchema);

async function testServiceCompletion() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test 1: Create a scheduled client for today
    console.log('\n=== Test 1: Creating scheduled client for today ===');
    const today = new Date();
    const scheduledClient = new Client({
      name: 'Test Scheduled Client - Today',
      phone: '+919876543220',
      location: 'Test Location - Today Service',
      notes: 'Test scheduled client for service completion',
      serviceType: 'scheduled',
      scheduledDate: today
    });
    
    await scheduledClient.save();
    console.log('Scheduled client created:', {
      id: scheduledClient._id,
      name: scheduledClient.name,
      serviceType: scheduledClient.serviceType,
      nextServiceDate: scheduledClient.nextServiceDate
    });

    // Test 2: Create a recurring client that's overdue
    console.log('\n=== Test 2: Creating overdue recurring client ===');
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const overdueClient = new Client({
      name: 'Test Overdue Client',
      phone: '+919876543221',
      location: 'Test Location - Overdue Service',
      notes: 'Test overdue client for service completion',
      serviceType: 'recurring',
      serviceInterval: 3,
      lastServiceDate: new Date(Date.now() - 4 * 30 * 24 * 60 * 60 * 1000) // 4 months ago
    });
    
    await overdueClient.save();
    console.log('Overdue client created:', {
      id: overdueClient._id,
      name: overdueClient.name,
      serviceType: overdueClient.serviceType,
      nextServiceDate: overdueClient.nextServiceDate,
      lastServiceDate: overdueClient.lastServiceDate
    });

    // Test 3: Simulate service completion for scheduled client
    console.log('\n=== Test 3: Completing service for scheduled client ===');
    
    // Create service record
    const scheduledService = new Service({
      clientId: scheduledClient._id,
      serviceDate: new Date(),
      notes: 'Test service completion'
    });
    await scheduledService.save();
    console.log('Service record created:', scheduledService._id);

    // Complete the service
    scheduledService.status = 'completed';
    scheduledService.notes = 'Service completed successfully';
    await scheduledService.save();
    console.log('Service marked as completed');

    // Update client's last service date
    scheduledClient.lastServiceDate = scheduledService.serviceDate;
    await scheduledClient.save();
    console.log('Client updated after service completion:', {
      id: scheduledClient._id,
      lastServiceDate: scheduledClient.lastServiceDate,
      nextServiceDate: scheduledClient.nextServiceDate
    });

    // Test 4: Simulate service completion for overdue client
    console.log('\n=== Test 4: Completing service for overdue client ===');
    
    // Create service record
    const overdueService = new Service({
      clientId: overdueClient._id,
      serviceDate: new Date(),
      notes: 'Test overdue service completion'
    });
    await overdueService.save();
    console.log('Service record created:', overdueService._id);

    // Complete the service
    overdueService.status = 'completed';
    overdueService.notes = 'Overdue service completed successfully';
    await overdueService.save();
    console.log('Service marked as completed');

    // Update client's last service date
    overdueClient.lastServiceDate = overdueService.serviceDate;
    await overdueClient.save();
    console.log('Client updated after service completion:', {
      id: overdueClient._id,
      lastServiceDate: overdueClient.lastServiceDate,
      nextServiceDate: overdueClient.nextServiceDate
    });

    // Test 5: Verify dashboard data after completions
    console.log('\n=== Test 5: Verifying dashboard data after completions ===');
    
    const todayStart = new Date(Date.UTC(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0));
    const todayEnd = new Date(Date.UTC(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999));
    
    const todayServices = await Client.find({
      nextServiceDate: {
        $gte: todayStart,
        $lte: todayEnd
      }
    });

    const overdueServices = await Client.find({
      nextServiceDate: {
        $lt: todayStart
      }
    });

    console.log('Dashboard data after service completions:', {
      todayCount: todayServices.length,
      overdueCount: overdueServices.length,
      todayClients: todayServices.map(c => ({ name: c.name, nextServiceDate: c.nextServiceDate })),
      overdueClients: overdueServices.map(c => ({ name: c.name, nextServiceDate: c.nextServiceDate }))
    });

    console.log('\n=== Service completion test completed successfully ===');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testServiceCompletion();
