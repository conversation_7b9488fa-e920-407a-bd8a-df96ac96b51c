'use client';

import React from 'react';
import { Bell, X, Calendar, AlertCircle } from 'lucide-react';
import { useNotification } from '@/contexts/NotificationContext';

const DailyNotification: React.FC = () => {
  const { showDailyNotification, todayTasksCount, dismissNotification } = useNotification();

  if (!showDailyNotification) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm w-full">
      <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg shadow-lg p-4 border border-orange-200">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <Bell className="w-4 h-4 text-white" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-white mb-1">
                Daily Service Reminder
              </h3>
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="w-4 h-4 text-orange-100" />
                <span className="text-sm text-orange-100">
                  {todayTasksCount} service{todayTasksCount !== 1 ? 's' : ''} scheduled for today
                </span>
              </div>
              <p className="text-xs text-orange-100">
                Don't forget to check your dashboard for today's tasks!
              </p>
            </div>
          </div>
          <button
            onClick={dismissNotification}
            className="flex-shrink-0 ml-2 p-1 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
            aria-label="Dismiss notification"
          >
            <X className="w-4 h-4 text-white" />
          </button>
        </div>
        
        {/* Progress indicator */}
        <div className="mt-3 flex items-center space-x-2">
          <AlertCircle className="w-4 h-4 text-orange-200" />
          <div className="flex-1 bg-white bg-opacity-20 rounded-full h-2">
            <div 
              className="bg-white h-2 rounded-full transition-all duration-1000"
              style={{ width: '100%' }}
            />
          </div>
          <span className="text-xs text-orange-100 font-medium">
            {todayTasksCount} pending
          </span>
        </div>
      </div>
    </div>
  );
};

export default DailyNotification;
