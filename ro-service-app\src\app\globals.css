@import "tailwindcss";

:root {
  --background: #f9fafb;
  --foreground: #111827;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
}

/* Mobile-first responsive utilities */
.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Custom scrollbar for better mobile experience */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Touch-friendly button sizing */
@media (max-width: 768px) {
  button, .btn {
    min-height: 44px;
    min-width: 44px;
  }

  input, textarea, select {
    min-height: 44px;
  }
}

/* Improved focus states for accessibility */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}
