import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, isToday, isTomorrow, isYesterday, isPast, startOfDay, endOfDay } from 'date-fns'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string): string {
  const d = new Date(date);

  // Ensure we're working with a valid date
  if (isNaN(d.getTime())) {
    return 'Invalid Date';
  }

  // Create a date object that represents the date in local timezone
  const localDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());

  if (isToday(localDate)) {
    return 'Today';
  }

  if (isTomorrow(localDate)) {
    return 'Tomorrow';
  }

  if (isYesterday(localDate)) {
    return 'Yesterday';
  }

  return format(localDate, 'MMM dd, yyyy');
}

export function formatPhoneNumber(phone: string): string {
  // Format +919876543210 to +91 98765 43210
  if (phone.startsWith('+91') && phone.length === 13) {
    return `${phone.slice(0, 3)} ${phone.slice(3, 8)} ${phone.slice(8)}`;
  }
  return phone;
}

export function getServiceStatus(nextServiceDate: Date): 'overdue' | 'today' | 'tomorrow' | 'upcoming' {
  const today = new Date();
  const localToday = startOfDay(new Date(today.getFullYear(), today.getMonth(), today.getDate()));

  const serviceDate = new Date(nextServiceDate);
  const localServiceDate = startOfDay(new Date(serviceDate.getFullYear(), serviceDate.getMonth(), serviceDate.getDate()));

  if (localServiceDate < localToday) {
    return 'overdue';
  }

  if (isToday(localServiceDate)) {
    return 'today';
  }

  if (isTomorrow(localServiceDate)) {
    return 'tomorrow';
  }

  return 'upcoming';
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'overdue':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'today':
      return 'text-orange-600 bg-orange-50 border-orange-200';
    case 'tomorrow':
      return 'text-blue-600 bg-blue-50 border-blue-200';
    case 'upcoming':
      return 'text-green-600 bg-green-50 border-green-200';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200';
  }
}

export function calculateNextServiceDate(lastServiceDate: Date, intervalMonths: number): Date {
  const nextDate = new Date(lastServiceDate);
  nextDate.setMonth(nextDate.getMonth() + intervalMonths);
  return nextDate;
}

export function getTodayDateRange() {
  const today = new Date();
  // Create date range in UTC to match database storage
  const year = today.getFullYear();
  const month = today.getMonth();
  const date = today.getDate();

  return {
    start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),
    end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))
  };
}

export function getTomorrowDateRange() {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const date = today.getDate() + 1;

  return {
    start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),
    end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))
  };
}
