import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Service from '@/models/Service';
import Client from '@/models/Client';
import mongoose from 'mongoose';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid service ID' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    const { notes } = body;
    
    // Find the service
    const service = await Service.findById(id);
    if (!service) {
      return NextResponse.json(
        { success: false, error: 'Service not found' },
        { status: 404 }
      );
    }
    
    // Update service status to completed
    service.status = 'completed';
    service.notes = notes || service.notes;
    service.completedAt = new Date();
    await service.save();
    
    // Update client's last service date and calculate next service date
    const client = await Client.findById(service.clientId);
    if (!client) {
      return NextResponse.json(
        { success: false, error: 'Client not found' },
        { status: 404 }
      );
    }

    console.log('Service completion - updating client:', {
      clientId: client._id,
      oldLastServiceDate: client.lastServiceDate,
      newLastServiceDate: service.serviceDate,
      serviceType: client.serviceType
    });

    client.lastServiceDate = service.serviceDate;
    // The pre-validate hook will automatically calculate the next service date
    await client.save();

    console.log('Service completion - client updated:', {
      clientId: client._id,
      newLastServiceDate: client.lastServiceDate,
      newNextServiceDate: client.nextServiceDate
    });

    return NextResponse.json({
      success: true,
      data: {
        service,
        client: {
          _id: client._id,
          name: client.name,
          phone: client.phone,
          location: client.location,
          notes: client.notes,
          serviceType: client.serviceType,
          serviceInterval: client.serviceInterval,
          scheduledDate: client.scheduledDate,
          lastServiceDate: client.lastServiceDate,
          nextServiceDate: client.nextServiceDate,
          createdAt: client.createdAt,
          updatedAt: client.updatedAt
        }
      },
      message: 'Service completed successfully'
    });
  } catch (error) {
    console.error('Error completing service:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to complete service' },
      { status: 500 }
    );
  }
}
