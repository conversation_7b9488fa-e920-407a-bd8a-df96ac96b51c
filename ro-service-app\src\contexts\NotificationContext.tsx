'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface NotificationContextType {
  showDailyNotification: boolean;
  todayTasksCount: number;
  dismissNotification: () => void;
  checkDailyNotification: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [showDailyNotification, setShowDailyNotification] = useState(false);
  const [todayTasksCount, setTodayTasksCount] = useState(0);

  const checkDailyNotification = async () => {
    try {
      const now = new Date();
      const currentHour = now.getHours();
      
      // Check if it's after 9 AM
      if (currentHour >= 9) {
        // Check if notification was already shown today
        const lastShown = localStorage.getItem('lastNotificationDate');
        const today = now.toDateString();
        
        if (lastShown !== today) {
          // Fetch today's tasks count
          const response = await fetch('/api/dashboard');
          const result = await response.json();
          
          if (result.success && result.data.stats.todayCount > 0) {
            setTodayTasksCount(result.data.stats.todayCount);
            setShowDailyNotification(true);
            localStorage.setItem('lastNotificationDate', today);
          }
        }
      }
    } catch (error) {
      console.error('Error checking daily notification:', error);
    }
  };

  const dismissNotification = () => {
    setShowDailyNotification(false);
  };

  useEffect(() => {
    // Check immediately on mount
    checkDailyNotification();
    
    // Set up interval to check every minute for the 9 AM trigger
    const interval = setInterval(() => {
      const now = new Date();
      if (now.getHours() === 9 && now.getMinutes() === 0) {
        checkDailyNotification();
      }
    }, 60000); // Check every minute
    
    return () => clearInterval(interval);
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        showDailyNotification,
        todayTasksCount,
        dismissNotification,
        checkDailyNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
