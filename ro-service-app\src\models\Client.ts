import mongoose, { Document, Schema } from 'mongoose';

export interface IClient extends Document {
  name: string;
  phone: string;
  location: string;
  notes?: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval?: number; // in months (for recurring services)
  scheduledDate?: Date; // for scheduled services
  lastServiceDate?: Date;
  nextServiceDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ClientSchema = new Schema<IClient>({
  name: {
    type: String,
    required: [true, 'Client name is required'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    validate: {
      validator: function(v: string) {
        return /^\+91[6-9]\d{9}$/.test(v);
      },
      message: 'Please enter a valid Indian phone number with +91 prefix'
    }
  },
  location: {
    type: String,
    required: [true, 'Service location is required'],
    trim: true,
    maxlength: [500, 'Location cannot be more than 500 characters']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  serviceType: {
    type: String,
    enum: ['recurring', 'scheduled'],
    default: 'recurring',
    required: [true, 'Service type is required']
  },
  serviceInterval: {
    type: Number,
    enum: [1, 2, 3, 4, 5, 6, 12],
    default: 3,
    required: function() {
      return this.serviceType === 'recurring';
    }
  },
  scheduledDate: {
    type: Date,
    required: function() {
      return this.serviceType === 'scheduled';
    }
  },
  lastServiceDate: {
    type: Date,
    default: null
  },
  nextServiceDate: {
    type: Date,
    required: true,
    default: function() {
      const baseDate = this.lastServiceDate || new Date();
      const nextDate = new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate());
      nextDate.setMonth(nextDate.getMonth() + (this.serviceInterval || 3));
      return nextDate;
    }
  }
}, {
  timestamps: true
});

// Calculate next service date before validation
ClientSchema.pre('validate', function(next) {
  // Always recalculate nextServiceDate to ensure consistency
  if (this.serviceType === 'scheduled') {
    // For scheduled services, use the scheduled date (store as UTC date-only)
    if (this.scheduledDate) {
      const schedDate = new Date(this.scheduledDate);
      // Use the exact date from scheduledDate
      this.nextServiceDate = new Date(Date.UTC(schedDate.getUTCFullYear(), schedDate.getUTCMonth(), schedDate.getUTCDate()));
    }
  } else {
    // For recurring services, calculate based on interval (store as UTC date-only)
    const baseDate = this.lastServiceDate || new Date();
    const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));
    nextDate.setUTCMonth(nextDate.getUTCMonth() + (this.serviceInterval || 3));
    this.nextServiceDate = nextDate;
  }
  next();
});

// Index for efficient queries
ClientSchema.index({ nextServiceDate: 1 });
ClientSchema.index({ name: 'text', location: 'text' });

export default mongoose.models.Client || mongoose.model<IClient>('Client', ClientSchema);
