'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Search, Plus, Users, AlertTriangle, Edit, Trash2 } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import ClientCard from '@/components/ClientCard';

interface Client {
  _id: string;
  name: string;
  phone: string;
  location: string;
  notes?: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval?: number;
  scheduledDate?: string;
  nextServiceDate: string;
  createdAt: string;
  updatedAt: string;
}

const ClientsPage = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [filteredClients, setFilteredClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [deleteLoading, setDeleteLoading] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    fetchClients();
  }, []);

  useEffect(() => {
    // Filter clients based on search term
    if (searchTerm.trim() === '') {
      setFilteredClients(clients);
    } else {
      const filtered = clients.filter(client =>
        client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        client.phone.includes(searchTerm) ||
        client.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredClients(filtered);
    }
  }, [searchTerm, clients]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      console.log('Clients page - fetching clients...');
      const response = await fetch('/api/clients');
      const result = await response.json();

      console.log('Clients page - API response:', result);

      if (result.success) {
        setClients(result.data);
        setFilteredClients(result.data);
        console.log('Clients page - clients set successfully:', {
          clientCount: result.data.length,
          sampleClients: result.data.slice(0, 3).map((c: Client) => ({
            id: c._id,
            name: c.name,
            serviceType: c.serviceType,
            nextServiceDate: c.nextServiceDate
          }))
        });
      } else {
        setError(result.error || 'Failed to fetch clients');
        console.error('Clients page - API error:', result.error);
      }
    } catch (err) {
      setError('Failed to fetch clients');
      console.error('Clients fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleServiceComplete = async (clientId: string) => {
    // Refresh clients data after service completion
    await fetchClients();
  };

  const handleDeleteClient = async (clientId: string) => {
    try {
      setDeleteLoading(clientId);
      const response = await fetch(`/api/clients/${clientId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        // Remove client from state
        setClients(prev => prev.filter(client => client._id !== clientId));
        setShowDeleteConfirm(null);
      } else {
        setError(result.error || 'Failed to delete client');
      }
    } catch (err) {
      setError('Failed to delete client');
      console.error('Delete error:', err);
    } finally {
      setDeleteLoading(null);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Clients</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchClients}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Clients</h1>
          <p className="text-gray-600">Manage your RO service clients</p>
        </div>
        <Link
          href="/clients/new"
          className="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add New Client
        </Link>
      </div>

      {/* Search Bar */}
      <div className="mb-6">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search clients by name, phone, or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <div className="flex items-center">
          <Users className="w-8 h-8 text-blue-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {filteredClients.length} {filteredClients.length === 1 ? 'Client' : 'Clients'}
            </h3>
            {searchTerm && (
              <p className="text-sm text-gray-600">
                {filteredClients.length} of {clients.length} clients match your search
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Clients Grid */}
      {filteredClients.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredClients.map((client) => (
            <div key={client._id} className="bg-white rounded-lg shadow-sm border p-4 sm:p-6 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1 min-w-0 pr-3">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">{client.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{client.phone}</p>
                  <p className="text-sm text-gray-600 line-clamp-2">{client.location}</p>
                </div>
                <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 flex-shrink-0">
                  <Link
                    href={`/clients/${client._id}/edit`}
                    className="p-2.5 sm:p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors touch-manipulation"
                    title="Edit client"
                  >
                    <Edit className="h-4 w-4 sm:h-4 sm:w-4" />
                  </Link>
                  <button
                    onClick={() => setShowDeleteConfirm(client._id)}
                    className="p-2.5 sm:p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors touch-manipulation"
                    title="Delete client"
                  >
                    <Trash2 className="h-4 w-4 sm:h-4 sm:w-4" />
                  </button>
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">
                    {client.serviceType === 'scheduled' ? 'Scheduled Service:' : 'Next Service:'}
                  </span>
                  <span className="font-medium text-gray-900">
                    {formatDate(client.nextServiceDate)}
                  </span>
                </div>
                {client.serviceType === 'recurring' && client.serviceInterval && (
                  <div className="flex justify-between items-center text-sm mt-1">
                    <span className="text-gray-600">Interval:</span>
                    <span className="text-gray-900">
                      {client.serviceInterval} {client.serviceInterval === 1 ? 'month' : 'months'}
                    </span>
                  </div>
                )}
                {client.notes && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600 line-clamp-2">{client.notes}</p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          {searchTerm ? (
            <>
              <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
              <p className="text-gray-600 mb-6">
                No clients match your search for &quot;{searchTerm}&quot;. Try a different search term.
              </p>
              <button
                onClick={() => setSearchTerm('')}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Clear search
              </button>
            </>
          ) : (
            <>
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No clients yet</h3>
              <p className="text-gray-600 mb-6">
                Get started by adding your first client to the system.
              </p>
              <Link
                href="/clients/new"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add Your First Client
              </Link>
            </>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
              <h3 className="text-lg font-semibold text-gray-900">Delete Client</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this client? This action cannot be undone and will remove all associated service records.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDeleteClient(showDeleteConfirm)}
                disabled={deleteLoading === showDeleteConfirm}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {deleteLoading === showDeleteConfirm ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </button>
              <button
                onClick={() => setShowDeleteConfirm(null)}
                disabled={deleteLoading === showDeleteConfirm}
                className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientsPage;
