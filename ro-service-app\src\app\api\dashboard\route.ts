import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Client from '@/models/Client';
import { getTodayDateRange, getTomorrowDateRange } from '@/lib/utils';

export async function GET() {
  try {
    await dbConnect();

    const today = getTodayDateRange();
    const tomorrow = getTomorrowDateRange();

    console.log('Dashboard API - Date ranges:', {
      today: { start: today.start, end: today.end },
      tomorrow: { start: tomorrow.start, end: tomorrow.end }
    });
    
    // Get today's pending services
    const todayServices = await Client.find({
      nextServiceDate: {
        $gte: today.start,
        $lte: today.end
      }
    }).sort({ nextServiceDate: 1 });
    
    // Get tomorrow's scheduled services
    const tomorrowServices = await Client.find({
      nextServiceDate: {
        $gte: tomorrow.start,
        $lte: tomorrow.end
      }
    }).sort({ nextServiceDate: 1 });
    
    // Get overdue services
    const overdueServices = await Client.find({
      nextServiceDate: {
        $lt: today.start
      }
    }).sort({ nextServiceDate: 1 });
    
    // Get upcoming services (next 7 days excluding today and tomorrow)
    const today_date = new Date();
    const nextWeek = new Date(Date.UTC(
      today_date.getFullYear(),
      today_date.getMonth(),
      today_date.getDate() + 7,
      23, 59, 59, 999
    )); // End of day in UTC to match database storage

    const upcomingServices = await Client.find({
      nextServiceDate: {
        $gt: tomorrow.end,
        $lte: nextWeek
      }
    }).sort({ nextServiceDate: 1 });

    console.log('Dashboard API - Query results:', {
      todayCount: todayServices.length,
      tomorrowCount: tomorrowServices.length,
      overdueCount: overdueServices.length,
      upcomingCount: upcomingServices.length,
      totalClients: await Client.countDocuments()
    });

    return NextResponse.json({
      success: true,
      data: {
        today: todayServices,
        tomorrow: tomorrowServices,
        overdue: overdueServices,
        upcoming: upcomingServices,
        stats: {
          totalClients: await Client.countDocuments(),
          todayCount: todayServices.length,
          tomorrowCount: tomorrowServices.length,
          overdueCount: overdueServices.length,
          upcomingCount: upcomingServices.length
        }
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch dashboard data' },
      { status: 500 }
    );
  }
}
