import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Client from '@/models/Client';
import { getTodayDateRange, getTomorrowDateRange } from '@/lib/utils';

export async function GET() {
  console.log('Dashboard API - Starting request');

  try {
    // Validate environment variables
    if (!process.env.MONGODB_URI) {
      console.error('Dashboard API - MONGODB_URI not found in environment variables');
      return NextResponse.json(
        { success: false, error: 'Database configuration error' },
        { status: 500 }
      );
    }

    console.log('Dashboard API - Attempting database connection');
    await dbConnect();
    console.log('Dashboard API - Database connected successfully');

    // Validate date utility functions
    let today, tomorrow;
    try {
      today = getTodayDateRange();
      tomorrow = getTomorrowDateRange();
      console.log('Dashboard API - Date ranges calculated:', {
        today: { start: today.start, end: today.end },
        tomorrow: { start: tomorrow.start, end: tomorrow.end }
      });
    } catch (dateError) {
      console.error('Dashboard API - Error calculating date ranges:', dateError);
      return NextResponse.json(
        { success: false, error: 'Date calculation error' },
        { status: 500 }
      );
    }

    // Validate Client model is available
    if (!Client) {
      console.error('Dashboard API - Client model not available');
      return NextResponse.json(
        { success: false, error: 'Database model error' },
        { status: 500 }
      );
    }

    console.log('Dashboard API - Starting database queries');

    // Get today's pending services with error handling
    let todayServices, tomorrowServices, overdueServices;
    try {
      todayServices = await Client.find({
        nextServiceDate: {
          $gte: today.start,
          $lte: today.end
        }
      }).sort({ nextServiceDate: 1 });
      console.log('Dashboard API - Today services query completed:', todayServices.length);
    } catch (queryError) {
      console.error('Dashboard API - Error querying today services:', queryError);
      return NextResponse.json(
        { success: false, error: 'Database query error for today services' },
        { status: 500 }
      );
    }

    try {
      // Get tomorrow's scheduled services
      tomorrowServices = await Client.find({
        nextServiceDate: {
          $gte: tomorrow.start,
          $lte: tomorrow.end
        }
      }).sort({ nextServiceDate: 1 });
      console.log('Dashboard API - Tomorrow services query completed:', tomorrowServices.length);
    } catch (queryError) {
      console.error('Dashboard API - Error querying tomorrow services:', queryError);
      return NextResponse.json(
        { success: false, error: 'Database query error for tomorrow services' },
        { status: 500 }
      );
    }

    try {
      // Get overdue services
      overdueServices = await Client.find({
        nextServiceDate: {
          $lt: today.start
        }
      }).sort({ nextServiceDate: 1 });
      console.log('Dashboard API - Overdue services query completed:', overdueServices.length);
    } catch (queryError) {
      console.error('Dashboard API - Error querying overdue services:', queryError);
      return NextResponse.json(
        { success: false, error: 'Database query error for overdue services' },
        { status: 500 }
      );
    }

    // Get upcoming services (next 7 days excluding today and tomorrow)
    let upcomingServices;
    try {
      const today_date = new Date();
      const nextWeek = new Date(Date.UTC(
        today_date.getFullYear(),
        today_date.getMonth(),
        today_date.getDate() + 7,
        23, 59, 59, 999
      )); // End of day in UTC to match database storage

      upcomingServices = await Client.find({
        nextServiceDate: {
          $gt: tomorrow.end,
          $lte: nextWeek
        }
      }).sort({ nextServiceDate: 1 });
      console.log('Dashboard API - Upcoming services query completed:', upcomingServices.length);
    } catch (queryError) {
      console.error('Dashboard API - Error querying upcoming services:', queryError);
      return NextResponse.json(
        { success: false, error: 'Database query error for upcoming services' },
        { status: 500 }
      );
    }

    // Get total client count with error handling
    let totalClients;
    try {
      totalClients = await Client.countDocuments();
      console.log('Dashboard API - Total clients count:', totalClients);
    } catch (countError) {
      console.error('Dashboard API - Error counting total clients:', countError);
      totalClients = 0; // Fallback to 0 instead of failing
    }

    console.log('Dashboard API - All queries completed successfully:', {
      todayCount: todayServices.length,
      tomorrowCount: tomorrowServices.length,
      overdueCount: overdueServices.length,
      upcomingCount: upcomingServices.length,
      totalClients
    });

    // Ensure all arrays are valid before returning
    const responseData = {
      today: Array.isArray(todayServices) ? todayServices : [],
      tomorrow: Array.isArray(tomorrowServices) ? tomorrowServices : [],
      overdue: Array.isArray(overdueServices) ? overdueServices : [],
      upcoming: Array.isArray(upcomingServices) ? upcomingServices : [],
      stats: {
        totalClients: totalClients || 0,
        todayCount: Array.isArray(todayServices) ? todayServices.length : 0,
        tomorrowCount: Array.isArray(tomorrowServices) ? tomorrowServices.length : 0,
        overdueCount: Array.isArray(overdueServices) ? overdueServices.length : 0,
        upcomingCount: Array.isArray(upcomingServices) ? upcomingServices.length : 0
      }
    };

    console.log('Dashboard API - Returning successful response');
    return NextResponse.json({
      success: true,
      data: responseData
    });
  } catch (error) {
    console.error('Dashboard API - Unexpected error:', error);

    // Ensure we always return a JSON response, even for unexpected errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString()
    };

    console.error('Dashboard API - Error details:', errorDetails);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard data',
        details: process.env.NODE_ENV === 'development' ? errorDetails : undefined
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}

// Ensure the route is properly exported and handles all HTTP methods gracefully
export async function POST() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, error: 'Method not allowed' },
    { status: 405 }
  );
}
