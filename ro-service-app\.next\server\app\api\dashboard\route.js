/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/route";
exports.ids = ["app/api/dashboard/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=K%3A%5CRO%20service%20app%5Cro-service-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=K%3A%5CRO%20service%20app%5Cro-service-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=K%3A%5CRO%20service%20app%5Cro-service-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=K%3A%5CRO%20service%20app%5Cro-service-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var K_RO_service_app_ro_service_app_src_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/dashboard/route.ts */ \"(rsc)/./src/app/api/dashboard/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/route\",\n        pathname: \"/api/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/route\"\n    },\n    distDir: \".next\" || 0,\n    relativeProjectDir:  false || '',\n    resolvedPagePath: \"K:\\\\RO service app\\\\ro-service-app\\\\src\\\\app\\\\api\\\\dashboard\\\\route.ts\",\n    nextConfigOutput,\n    userland: K_RO_service_app_ro_service_app_src_app_api_dashboard_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/dashboard/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = false;\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                cacheComponents: Boolean(nextConfig.experimental.cacheComponents),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=K%3A%5CRO%20service%20app%5Cro-service-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=K%3A%5CRO%20service%20app%5Cro-service-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/dashboard/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mongodb__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mongodb */ \"(rsc)/./src/lib/mongodb.ts\");\n/* harmony import */ var _models_Client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/models/Client */ \"(rsc)/./src/models/Client.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nasync function GET() {\n    try {\n        await (0,_lib_mongodb__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const today = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getTodayDateRange)();\n        const tomorrow = (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getTomorrowDateRange)();\n        console.log('Dashboard API - Date ranges:', {\n            today: {\n                start: today.start,\n                end: today.end\n            },\n            tomorrow: {\n                start: tomorrow.start,\n                end: tomorrow.end\n            }\n        });\n        // Get today's pending services\n        const todayServices = await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            nextServiceDate: {\n                $gte: today.start,\n                $lte: today.end\n            }\n        }).sort({\n            nextServiceDate: 1\n        });\n        // Get tomorrow's scheduled services\n        const tomorrowServices = await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            nextServiceDate: {\n                $gte: tomorrow.start,\n                $lte: tomorrow.end\n            }\n        }).sort({\n            nextServiceDate: 1\n        });\n        // Get overdue services\n        const overdueServices = await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            nextServiceDate: {\n                $lt: today.start\n            }\n        }).sort({\n            nextServiceDate: 1\n        });\n        // Get upcoming services (next 7 days excluding today and tomorrow)\n        const today_date = new Date();\n        const nextWeek = new Date(Date.UTC(today_date.getFullYear(), today_date.getMonth(), today_date.getDate() + 7, 23, 59, 59, 999)); // End of day in UTC to match database storage\n        const upcomingServices = await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find({\n            nextServiceDate: {\n                $gt: tomorrow.end,\n                $lte: nextWeek\n            }\n        }).sort({\n            nextServiceDate: 1\n        });\n        console.log('Dashboard API - Query results:', {\n            todayCount: todayServices.length,\n            tomorrowCount: tomorrowServices.length,\n            overdueCount: overdueServices.length,\n            upcomingCount: upcomingServices.length,\n            totalClients: await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments()\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                today: todayServices,\n                tomorrow: tomorrowServices,\n                overdue: overdueServices,\n                upcoming: upcomingServices,\n                stats: {\n                    totalClients: await _models_Client__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(),\n                    todayCount: todayServices.length,\n                    tomorrowCount: tomorrowServices.length,\n                    overdueCount: overdueServices.length,\n                    upcomingCount: upcomingServices.length\n                }\n            }\n        });\n    } catch (error) {\n        console.error('Error fetching dashboard data:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to fetch dashboard data'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mongodb.ts":
/*!****************************!*\
  !*** ./src/lib/mongodb.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst MONGODB_URI = process.env.MONGODB_URI;\nif (!MONGODB_URI) {\n    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */ let cached = global.mongoose;\nif (!cached) {\n    cached = global.mongoose = {\n        conn: null,\n        promise: null\n    };\n}\nasync function dbConnect() {\n    if (cached.conn) {\n        return cached.conn;\n    }\n    if (!cached.promise) {\n        const opts = {\n            bufferCommands: false\n        };\n        cached.promise = mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(MONGODB_URI, opts).then((mongoose)=>{\n            return mongoose;\n        });\n    }\n    try {\n        cached.conn = await cached.promise;\n    } catch (e) {\n        cached.promise = null;\n        throw e;\n    }\n    return cached.conn;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dbConnect);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mongodb.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNextServiceDate: () => (/* binding */ calculateNextServiceDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   getServiceStatus: () => (/* binding */ getServiceStatus),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getTodayDateRange: () => (/* binding */ getTodayDateRange),\n/* harmony export */   getTomorrowDateRange: () => (/* binding */ getTomorrowDateRange)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(rsc)/./node_modules/date-fns/isToday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(rsc)/./node_modules/date-fns/isTomorrow.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(rsc)/./node_modules/date-fns/isYesterday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(rsc)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(rsc)/./node_modules/date-fns/startOfDay.js\");\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    // Ensure we're working with a valid date\n    if (isNaN(d.getTime())) {\n        return 'Invalid Date';\n    }\n    // Create a date object that represents the date in local timezone\n    const localDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localDate)) {\n        return 'Today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localDate)) {\n        return 'Tomorrow';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__.isYesterday)(localDate)) {\n        return 'Yesterday';\n    }\n    return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(localDate, 'MMM dd, yyyy');\n}\nfunction formatPhoneNumber(phone) {\n    // Format +919876543210 to +91 98765 43210\n    if (phone.startsWith('+91') && phone.length === 13) {\n        return `${phone.slice(0, 3)} ${phone.slice(3, 8)} ${phone.slice(8)}`;\n    }\n    return phone;\n}\nfunction getServiceStatus(nextServiceDate) {\n    const today = new Date();\n    const localToday = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(today.getFullYear(), today.getMonth(), today.getDate()));\n    const serviceDate = new Date(nextServiceDate);\n    const localServiceDate = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(serviceDate.getFullYear(), serviceDate.getMonth(), serviceDate.getDate()));\n    if (localServiceDate < localToday) {\n        return 'overdue';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localServiceDate)) {\n        return 'today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localServiceDate)) {\n        return 'tomorrow';\n    }\n    return 'upcoming';\n}\nfunction getStatusColor(status) {\n    switch(status){\n        case 'overdue':\n            return 'text-red-600 bg-red-50 border-red-200';\n        case 'today':\n            return 'text-orange-600 bg-orange-50 border-orange-200';\n        case 'tomorrow':\n            return 'text-blue-600 bg-blue-50 border-blue-200';\n        case 'upcoming':\n            return 'text-green-600 bg-green-50 border-green-200';\n        default:\n            return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n}\nfunction calculateNextServiceDate(lastServiceDate, intervalMonths) {\n    const nextDate = new Date(lastServiceDate);\n    nextDate.setMonth(nextDate.getMonth() + intervalMonths);\n    return nextDate;\n}\nfunction getTodayDateRange() {\n    const today = new Date();\n    // Create date range in UTC to match database storage\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const date = today.getDate();\n    return {\n        start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),\n        end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))\n    };\n}\nfunction getTomorrowDateRange() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const date = today.getDate() + 1;\n    return {\n        start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),\n        end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/models/Client.ts":
/*!******************************!*\
  !*** ./src/models/Client.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ClientSchema = new mongoose__WEBPACK_IMPORTED_MODULE_0__.Schema({\n    name: {\n        type: String,\n        required: [\n            true,\n            'Client name is required'\n        ],\n        trim: true,\n        maxlength: [\n            100,\n            'Name cannot be more than 100 characters'\n        ]\n    },\n    phone: {\n        type: String,\n        required: [\n            true,\n            'Phone number is required'\n        ],\n        validate: {\n            validator: function(v) {\n                return /^\\+91[6-9]\\d{9}$/.test(v);\n            },\n            message: 'Please enter a valid Indian phone number with +91 prefix'\n        }\n    },\n    location: {\n        type: String,\n        required: [\n            true,\n            'Service location is required'\n        ],\n        trim: true,\n        maxlength: [\n            500,\n            'Location cannot be more than 500 characters'\n        ]\n    },\n    notes: {\n        type: String,\n        trim: true,\n        maxlength: [\n            1000,\n            'Notes cannot be more than 1000 characters'\n        ]\n    },\n    serviceType: {\n        type: String,\n        enum: [\n            'recurring',\n            'scheduled'\n        ],\n        default: 'recurring',\n        required: [\n            true,\n            'Service type is required'\n        ]\n    },\n    serviceInterval: {\n        type: Number,\n        enum: [\n            1,\n            2,\n            3,\n            4,\n            5,\n            6,\n            12\n        ],\n        default: 3,\n        required: function() {\n            return this.serviceType === 'recurring';\n        }\n    },\n    scheduledDate: {\n        type: Date,\n        required: function() {\n            return this.serviceType === 'scheduled';\n        }\n    },\n    lastServiceDate: {\n        type: Date,\n        default: null\n    },\n    nextServiceDate: {\n        type: Date,\n        required: true,\n        default: function() {\n            const baseDate = this.lastServiceDate || new Date();\n            const nextDate = new Date(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate());\n            nextDate.setMonth(nextDate.getMonth() + (this.serviceInterval || 3));\n            return nextDate;\n        }\n    }\n}, {\n    timestamps: true\n});\n// Calculate next service date before validation\nClientSchema.pre('validate', function(next) {\n    console.log('Client pre-validate hook - calculating nextServiceDate for:', {\n        id: this._id,\n        serviceType: this.serviceType,\n        serviceInterval: this.serviceInterval,\n        scheduledDate: this.scheduledDate,\n        lastServiceDate: this.lastServiceDate\n    });\n    // Always recalculate nextServiceDate to ensure consistency\n    if (this.serviceType === 'scheduled') {\n        // For scheduled services, use the scheduled date (store as UTC date-only)\n        if (this.scheduledDate) {\n            const schedDate = new Date(this.scheduledDate);\n            // Use the exact date from scheduledDate\n            this.nextServiceDate = new Date(Date.UTC(schedDate.getUTCFullYear(), schedDate.getUTCMonth(), schedDate.getUTCDate()));\n            console.log('Calculated nextServiceDate for scheduled service:', this.nextServiceDate);\n        } else {\n            console.error('Scheduled service missing scheduledDate');\n        }\n    } else {\n        // For recurring services, calculate based on interval (store as UTC date-only)\n        const baseDate = this.lastServiceDate || new Date();\n        const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));\n        const interval = this.serviceInterval || 3;\n        nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);\n        this.nextServiceDate = nextDate;\n        console.log('Calculated nextServiceDate for recurring service:', {\n            baseDate,\n            interval,\n            nextServiceDate: this.nextServiceDate\n        });\n    }\n    next();\n});\n// Index for efficient queries\nClientSchema.index({\n    nextServiceDate: 1\n});\nClientSchema.index({\n    name: 'text',\n    location: 'text'\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Client || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model('Client', ClientSchema));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/models/Client.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.ts&appDir=K%3A%5CRO%20service%20app%5Cro-service-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=K%3A%5CRO%20service%20app%5Cro-service-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();