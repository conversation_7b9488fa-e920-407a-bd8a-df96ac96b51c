import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import { NotificationProvider } from "@/contexts/NotificationContext";
import DailyNotification from "@/components/DailyNotification";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "RO Service Manager",
  description: "Comprehensive RO water service management application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased bg-gray-50`}>
        <NotificationProvider>
          <div className="min-h-screen">
            <Navigation />
            <main className="pb-16 md:pb-0">
              {children}
            </main>
            <DailyNotification />
          </div>
        </NotificationProvider>
      </body>
    </html>
  );
}
