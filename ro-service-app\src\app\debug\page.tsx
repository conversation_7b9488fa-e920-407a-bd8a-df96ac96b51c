'use client';

import { useState, useEffect } from 'react';
import { Al<PERSON><PERSON>riangle, CheckCircle, RefreshCw } from 'lucide-react';

interface Client {
  _id: string;
  name: string;
  phone: string;
  location: string;
  serviceType: 'recurring' | 'scheduled';
  serviceInterval?: number;
  scheduledDate?: string;
  nextServiceDate: string;
  createdAt: string;
  updatedAt: string;
}

interface DashboardData {
  today: Client[];
  tomorrow: Client[];
  overdue: Client[];
  upcoming: Client[];
  stats: {
    totalClients: number;
    todayCount: number;
    tomorrowCount: number;
    overdueCount: number;
    upcomingCount: number;
  };
}

export default function DebugPage() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [clientsData, setClientsData] = useState<Client[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Debug - Fetching dashboard data...');
      
      const response = await fetch('/api/dashboard');
      const result = await response.json();
      
      console.log('Debug - Dashboard API response:', result);
      
      if (result.success) {
        setDashboardData(result.data);
      } else {
        setError(`Dashboard API error: ${result.error}`);
      }
    } catch (err) {
      console.error('Debug - Dashboard fetch error:', err);
      setError(`Dashboard fetch error: ${err}`);
    }
  };

  const fetchClientsData = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Debug - Fetching clients data...');
      
      const response = await fetch('/api/clients');
      const result = await response.json();
      
      console.log('Debug - Clients API response:', result);
      
      if (result.success) {
        setClientsData(result.data);
      } else {
        setError(`Clients API error: ${result.error}`);
      }
    } catch (err) {
      console.error('Debug - Clients fetch error:', err);
      setError(`Clients fetch error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllData = async () => {
    await Promise.all([fetchDashboardData(), fetchClientsData()]);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleString();
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Debug Page</h1>
        <p className="text-gray-600 mb-4">Debug data flow and API responses</p>
        
        <button
          onClick={fetchAllData}
          disabled={loading}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Data
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      <div className="grid gap-8 lg:grid-cols-2">
        {/* Dashboard Data */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-green-600" />
            Dashboard Data
          </h2>
          
          {dashboardData ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Total Clients:</span> {dashboardData.stats.totalClients}
                </div>
                <div>
                  <span className="font-medium">Today:</span> {dashboardData.stats.todayCount}
                </div>
                <div>
                  <span className="font-medium">Tomorrow:</span> {dashboardData.stats.tomorrowCount}
                </div>
                <div>
                  <span className="font-medium">Overdue:</span> {dashboardData.stats.overdueCount}
                </div>
                <div>
                  <span className="font-medium">Upcoming:</span> {dashboardData.stats.upcomingCount}
                </div>
              </div>
              
              <div className="space-y-3">
                {['today', 'tomorrow', 'overdue', 'upcoming'].map((category) => {
                  const clients = dashboardData[category as keyof Omit<DashboardData, 'stats'>] as Client[];
                  return (
                    <div key={category} className="border rounded p-3">
                      <h3 className="font-medium text-gray-900 mb-2 capitalize">{category} ({clients.length})</h3>
                      {clients.length > 0 ? (
                        <div className="space-y-1 text-sm">
                          {clients.slice(0, 3).map((client) => (
                            <div key={client._id} className="text-gray-600">
                              {client.name} - {formatDate(client.nextServiceDate)}
                            </div>
                          ))}
                          {clients.length > 3 && (
                            <div className="text-gray-500">+{clients.length - 3} more...</div>
                          )}
                        </div>
                      ) : (
                        <div className="text-gray-500 text-sm">No clients</div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-gray-500">No dashboard data</div>
          )}
        </div>

        {/* Clients Data */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-blue-600" />
            Clients Data
          </h2>
          
          <div className="space-y-4">
            <div className="text-sm">
              <span className="font-medium">Total Clients:</span> {clientsData.length}
            </div>
            
            {clientsData.length > 0 ? (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {clientsData.map((client) => (
                  <div key={client._id} className="border rounded p-3 text-sm">
                    <div className="font-medium text-gray-900">{client.name}</div>
                    <div className="text-gray-600">Phone: {client.phone}</div>
                    <div className="text-gray-600">Type: {client.serviceType}</div>
                    {client.serviceInterval && (
                      <div className="text-gray-600">Interval: {client.serviceInterval} months</div>
                    )}
                    <div className="text-gray-600">Next Service: {formatDate(client.nextServiceDate)}</div>
                    <div className="text-gray-500 text-xs">Created: {formatDate(client.createdAt)}</div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-500">No clients found</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
