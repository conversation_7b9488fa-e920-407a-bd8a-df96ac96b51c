"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/clients/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNextServiceDate: () => (/* binding */ calculateNextServiceDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   getServiceStatus: () => (/* binding */ getServiceStatus),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getTodayDateRange: () => (/* binding */ getTodayDateRange),\n/* harmony export */   getTomorrowDateRange: () => (/* binding */ getTomorrowDateRange)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isToday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isTomorrow.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isYesterday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/startOfDay.js\");\n\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    // Ensure we're working with a valid date\n    if (isNaN(d.getTime())) {\n        return 'Invalid Date';\n    }\n    // Create a date object that represents the date in local timezone\n    const localDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localDate)) {\n        return 'Today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localDate)) {\n        return 'Tomorrow';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__.isYesterday)(localDate)) {\n        return 'Yesterday';\n    }\n    return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(localDate, 'MMM dd, yyyy');\n}\nfunction formatPhoneNumber(phone) {\n    // Format +919876543210 to +91 98765 43210\n    if (phone.startsWith('+91') && phone.length === 13) {\n        return \"\".concat(phone.slice(0, 3), \" \").concat(phone.slice(3, 8), \" \").concat(phone.slice(8));\n    }\n    return phone;\n}\nfunction getServiceStatus(nextServiceDate) {\n    const today = new Date();\n    const localToday = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(today.getFullYear(), today.getMonth(), today.getDate()));\n    const serviceDate = new Date(nextServiceDate);\n    const localServiceDate = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(serviceDate.getFullYear(), serviceDate.getMonth(), serviceDate.getDate()));\n    if (localServiceDate < localToday) {\n        return 'overdue';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localServiceDate)) {\n        return 'today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localServiceDate)) {\n        return 'tomorrow';\n    }\n    return 'upcoming';\n}\nfunction getStatusColor(status) {\n    switch(status){\n        case 'overdue':\n            return 'text-red-600 bg-red-50 border-red-200';\n        case 'today':\n            return 'text-orange-600 bg-orange-50 border-orange-200';\n        case 'tomorrow':\n            return 'text-blue-600 bg-blue-50 border-blue-200';\n        case 'upcoming':\n            return 'text-green-600 bg-green-50 border-green-200';\n        default:\n            return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n}\nfunction calculateNextServiceDate(lastServiceDate, intervalMonths) {\n    const nextDate = new Date(lastServiceDate);\n    nextDate.setMonth(nextDate.getMonth() + intervalMonths);\n    return nextDate;\n}\nfunction getTodayDateRange() {\n    try {\n        const today = new Date();\n        // Validate the date\n        if (isNaN(today.getTime())) {\n            throw new Error('Invalid date object created');\n        }\n        // Create date range in UTC to match database storage\n        const year = today.getFullYear();\n        const month = today.getMonth();\n        const date = today.getDate();\n        const start = new Date(Date.UTC(year, month, date, 0, 0, 0, 0));\n        const end = new Date(Date.UTC(year, month, date, 23, 59, 59, 999));\n        // Validate the created dates\n        if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n            throw new Error('Invalid date range created');\n        }\n        return {\n            start,\n            end\n        };\n    } catch (error) {\n        console.error('Error in getTodayDateRange:', error);\n        throw new Error(\"Failed to calculate today's date range: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\nfunction getTomorrowDateRange() {\n    try {\n        const today = new Date();\n        // Validate the date\n        if (isNaN(today.getTime())) {\n            throw new Error('Invalid date object created');\n        }\n        const year = today.getFullYear();\n        const month = today.getMonth();\n        const date = today.getDate() + 1;\n        const start = new Date(Date.UTC(year, month, date, 0, 0, 0, 0));\n        const end = new Date(Date.UTC(year, month, date, 23, 59, 59, 999));\n        // Validate the created dates\n        if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n            throw new Error('Invalid date range created');\n        }\n        return {\n            start,\n            end\n        };\n    } catch (error) {\n        console.error('Error in getTomorrowDateRange:', error);\n        throw new Error(\"Failed to calculate tomorrow's date range: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});