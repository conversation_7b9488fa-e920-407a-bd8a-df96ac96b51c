'use client';

import { useState } from 'react';
import { CheckCircle, X, Calendar, ArrowRight } from 'lucide-react';

interface RecurringServiceModalProps {
  isOpen: boolean;
  clientName: string;
  clientId: string;
  onClose: () => void;
  onYes: (clientId: string) => void;
  onNo: () => void;
}

const RecurringServiceModal = ({
  isOpen,
  clientName,
  clientId,
  onClose,
  onYes,
  onNo
}: RecurringServiceModalProps) => {
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handleYes = async () => {
    setIsProcessing(true);
    try {
      await onYes(clientId);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNo = () => {
    onNo();
    onClose();
  };

  const handleClose = () => {
    if (!isProcessing) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6 relative">
        {/* Close button */}
        <button
          onClick={handleClose}
          disabled={isProcessing}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 disabled:opacity-50"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Service Completed!</h3>
            <p className="text-sm text-gray-600">for {clientName}</p>
          </div>
        </div>

        {/* Content */}
        <div className="mb-6">
          <p className="text-gray-700 mb-4">
            Great! The service has been marked as completed. Would you like to set up recurring services for this client?
          </p>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <Calendar className="w-5 h-5 text-blue-600 mr-2 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">Recurring Services</h4>
                <p className="text-sm text-blue-700">
                  Set up automatic scheduling for regular maintenance visits (monthly, quarterly, etc.)
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-3">
          <button
            onClick={handleYes}
            disabled={isProcessing}
            className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Setting up...
              </>
            ) : (
              <>
                <Calendar className="w-4 h-4 mr-2" />
                Yes, Set Up Recurring
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </button>
          
          <button
            onClick={handleNo}
            disabled={isProcessing}
            className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 disabled:opacity-50 transition-colors"
          >
            No, Thanks
          </button>
        </div>

        {/* Additional info */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            You can always set up recurring services later from the client edit page
          </p>
        </div>
      </div>
    </div>
  );
};

export default RecurringServiceModal;
