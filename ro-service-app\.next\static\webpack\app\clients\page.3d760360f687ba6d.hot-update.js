"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/clients/page",{

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateNextServiceDate: () => (/* binding */ calculateNextServiceDate),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatPhoneNumber: () => (/* binding */ formatPhoneNumber),\n/* harmony export */   getServiceStatus: () => (/* binding */ getServiceStatus),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   getTodayDateRange: () => (/* binding */ getTodayDateRange),\n/* harmony export */   getTomorrowDateRange: () => (/* binding */ getTomorrowDateRange)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isToday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isTomorrow.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isYesterday.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/startOfDay.js\");\n\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    const d = new Date(date);\n    // Ensure we're working with a valid date\n    if (isNaN(d.getTime())) {\n        return 'Invalid Date';\n    }\n    // Create a date object that represents the date in local timezone\n    const localDate = new Date(d.getFullYear(), d.getMonth(), d.getDate());\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localDate)) {\n        return 'Today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localDate)) {\n        return 'Tomorrow';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__.isYesterday)(localDate)) {\n        return 'Yesterday';\n    }\n    return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(localDate, 'MMM dd, yyyy');\n}\nfunction formatPhoneNumber(phone) {\n    // Format +919876543210 to +91 98765 43210\n    if (phone.startsWith('+91') && phone.length === 13) {\n        return \"\".concat(phone.slice(0, 3), \" \").concat(phone.slice(3, 8), \" \").concat(phone.slice(8));\n    }\n    return phone;\n}\nfunction getServiceStatus(nextServiceDate) {\n    const today = new Date();\n    const localToday = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(today.getFullYear(), today.getMonth(), today.getDate()));\n    const serviceDate = new Date(nextServiceDate);\n    const localServiceDate = (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.startOfDay)(new Date(serviceDate.getFullYear(), serviceDate.getMonth(), serviceDate.getDate()));\n    if (localServiceDate < localToday) {\n        return 'overdue';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_2__.isToday)(localServiceDate)) {\n        return 'today';\n    }\n    if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_3__.isTomorrow)(localServiceDate)) {\n        return 'tomorrow';\n    }\n    return 'upcoming';\n}\nfunction getStatusColor(status) {\n    switch(status){\n        case 'overdue':\n            return 'text-red-600 bg-red-50 border-red-200';\n        case 'today':\n            return 'text-orange-600 bg-orange-50 border-orange-200';\n        case 'tomorrow':\n            return 'text-blue-600 bg-blue-50 border-blue-200';\n        case 'upcoming':\n            return 'text-green-600 bg-green-50 border-green-200';\n        default:\n            return 'text-gray-600 bg-gray-50 border-gray-200';\n    }\n}\nfunction calculateNextServiceDate(lastServiceDate, intervalMonths) {\n    const nextDate = new Date(lastServiceDate);\n    nextDate.setMonth(nextDate.getMonth() + intervalMonths);\n    return nextDate;\n}\nfunction getTodayDateRange() {\n    try {\n        const today = new Date();\n        // Validate the date\n        if (isNaN(today.getTime())) {\n            throw new Error('Invalid date object created');\n        }\n        // Create date range in UTC to match database storage\n        const year = today.getFullYear();\n        const month = today.getMonth();\n        const date = today.getDate();\n        const start = new Date(Date.UTC(year, month, date, 0, 0, 0, 0));\n        const end = new Date(Date.UTC(year, month, date, 23, 59, 59, 999));\n        // Validate the created dates\n        if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n            throw new Error('Invalid date range created');\n        }\n        return {\n            start,\n            end\n        };\n    } catch (error) {\n        console.error('Error in getTodayDateRange:', error);\n        throw new Error(\"Failed to calculate today's date range: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n    }\n}\nfunction getTomorrowDateRange() {\n    const today = new Date();\n    const year = today.getFullYear();\n    const month = today.getMonth();\n    const date = today.getDate() + 1;\n    return {\n        start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),\n        end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

});