// Test script to verify the dashboard API is returning JSON correctly
// Run this with: node test-dashboard-api.js

const http = require('http');

async function testDashboardAPI() {
  console.log('Testing Dashboard API...');
  
  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/dashboard',
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      console.log(`Status Code: ${res.statusCode}`);
      console.log(`Headers:`, res.headers);
      
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('\n=== Response Body ===');
        console.log(data);
        
        // Check if response is JSON
        try {
          const jsonData = JSON.parse(data);
          console.log('\n✅ Response is valid JSON');
          console.log('Parsed data:', JSON.stringify(jsonData, null, 2));
          
          // Validate expected structure
          if (jsonData.success !== undefined) {
            console.log('✅ Response has success field');
            
            if (jsonData.success === true) {
              console.log('✅ API call was successful');
              
              if (jsonData.data) {
                console.log('✅ Response has data field');
                
                const expectedFields = ['today', 'tomorrow', 'overdue', 'upcoming', 'stats'];
                const hasAllFields = expectedFields.every(field => 
                  jsonData.data.hasOwnProperty(field)
                );
                
                if (hasAllFields) {
                  console.log('✅ Response has all expected data fields');
                  console.log('Data summary:', {
                    todayCount: jsonData.data.today?.length || 0,
                    tomorrowCount: jsonData.data.tomorrow?.length || 0,
                    overdueCount: jsonData.data.overdue?.length || 0,
                    upcomingCount: jsonData.data.upcoming?.length || 0,
                    totalClients: jsonData.data.stats?.totalClients || 0
                  });
                } else {
                  console.log('❌ Response missing expected data fields');
                  console.log('Expected:', expectedFields);
                  console.log('Actual:', Object.keys(jsonData.data || {}));
                }
              } else {
                console.log('❌ Response missing data field');
              }
            } else {
              console.log('❌ API call failed:', jsonData.error || 'Unknown error');
            }
          } else {
            console.log('❌ Response missing success field');
          }
          
          resolve(jsonData);
        } catch (parseError) {
          console.log('\n❌ Response is NOT valid JSON');
          console.log('Parse error:', parseError.message);
          console.log('Raw response:', data.substring(0, 500) + (data.length > 500 ? '...' : ''));
          
          // Check if it's HTML
          if (data.trim().startsWith('<!DOCTYPE') || data.trim().startsWith('<html')) {
            console.log('❌ Response appears to be HTML instead of JSON');
            console.log('This indicates the API route is throwing an error and Next.js is serving an error page');
          }
          
          reject(new Error('Invalid JSON response'));
        }
      });
    });
    
    req.on('error', (error) => {
      console.log('❌ Request failed:', error.message);
      reject(error);
    });
    
    req.setTimeout(10000, () => {
      console.log('❌ Request timed out');
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

async function testWithRetry() {
  const maxRetries = 3;
  let attempt = 1;
  
  while (attempt <= maxRetries) {
    try {
      console.log(`\n=== Attempt ${attempt}/${maxRetries} ===`);
      await testDashboardAPI();
      console.log('\n🎉 Dashboard API test completed successfully!');
      return;
    } catch (error) {
      console.log(`\n❌ Attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxRetries) {
        console.log(`Retrying in 2 seconds...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      attempt++;
    }
  }
  
  console.log('\n💥 All attempts failed. Please check:');
  console.log('1. Is the Next.js development server running? (npm run dev)');
  console.log('2. Is MongoDB running and accessible?');
  console.log('3. Are there any errors in the server console?');
  console.log('4. Check the dashboard API route for syntax errors');
}

// Run the test
testWithRetry();
