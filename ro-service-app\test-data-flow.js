// Test script to verify data flow in the RO Service App
// Run this with: node test-data-flow.js

const mongoose = require('mongoose');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/ro-service-app';

// Client schema (simplified version for testing)
const ClientSchema = new mongoose.Schema({
  name: { type: String, required: true },
  phone: { type: String, required: true },
  location: { type: String, required: true },
  notes: String,
  serviceType: { type: String, enum: ['recurring', 'scheduled'], default: 'recurring' },
  serviceInterval: { type: Number, enum: [1, 2, 3, 4, 5, 6, 12], default: 3 },
  scheduledDate: Date,
  lastServiceDate: { type: Date, default: null },
  nextServiceDate: { type: Date, required: true }
}, { timestamps: true });

// Pre-validate hook (same as in the actual model)
ClientSchema.pre('validate', function(next) {
  console.log('Test - Client pre-validate hook triggered for:', {
    id: this._id,
    serviceType: this.serviceType,
    serviceInterval: this.serviceInterval,
    scheduledDate: this.scheduledDate,
    lastServiceDate: this.lastServiceDate
  });

  if (this.serviceType === 'scheduled') {
    if (this.scheduledDate) {
      const schedDate = new Date(this.scheduledDate);
      this.nextServiceDate = new Date(Date.UTC(schedDate.getUTCFullYear(), schedDate.getUTCMonth(), schedDate.getUTCDate()));
      console.log('Test - Calculated nextServiceDate for scheduled service:', this.nextServiceDate);
    }
  } else {
    const baseDate = this.lastServiceDate || new Date();
    const nextDate = new Date(Date.UTC(baseDate.getFullYear(), baseDate.getMonth(), baseDate.getDate()));
    const interval = this.serviceInterval || 3;
    nextDate.setUTCMonth(nextDate.getUTCMonth() + interval);
    this.nextServiceDate = nextDate;
    console.log('Test - Calculated nextServiceDate for recurring service:', {
      baseDate,
      interval,
      nextServiceDate: this.nextServiceDate
    });
  }
  next();
});

const Client = mongoose.model('Client', ClientSchema);

// Utility functions for date ranges (same as in utils.ts)
function getTodayDateRange() {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const date = today.getDate();

  return {
    start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),
    end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))
  };
}

function getTomorrowDateRange() {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const date = today.getDate() + 1;

  return {
    start: new Date(Date.UTC(year, month, date, 0, 0, 0, 0)),
    end: new Date(Date.UTC(year, month, date, 23, 59, 59, 999))
  };
}

async function testDataFlow() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Test 1: Create a recurring client
    console.log('\n=== Test 1: Creating recurring client ===');
    const recurringClient = new Client({
      name: 'Test Recurring Client',
      phone: '+919876543210',
      location: 'Test Location 1',
      notes: 'Test recurring client',
      serviceType: 'recurring',
      serviceInterval: 3
    });
    
    await recurringClient.save();
    console.log('Recurring client created:', {
      id: recurringClient._id,
      nextServiceDate: recurringClient.nextServiceDate
    });

    // Test 2: Create a scheduled client
    console.log('\n=== Test 2: Creating scheduled client ===');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const scheduledClient = new Client({
      name: 'Test Scheduled Client',
      phone: '+919876543211',
      location: 'Test Location 2',
      notes: 'Test scheduled client',
      serviceType: 'scheduled',
      scheduledDate: tomorrow
    });
    
    await scheduledClient.save();
    console.log('Scheduled client created:', {
      id: scheduledClient._id,
      nextServiceDate: scheduledClient.nextServiceDate
    });

    // Test 3: Query dashboard data
    console.log('\n=== Test 3: Querying dashboard data ===');
    const today = getTodayDateRange();
    const tomorrowRange = getTomorrowDateRange();
    
    console.log('Date ranges:', {
      today: { start: today.start, end: today.end },
      tomorrow: { start: tomorrowRange.start, end: tomorrowRange.end }
    });

    const todayServices = await Client.find({
      nextServiceDate: {
        $gte: today.start,
        $lte: today.end
      }
    });

    const tomorrowServices = await Client.find({
      nextServiceDate: {
        $gte: tomorrowRange.start,
        $lte: tomorrowRange.end
      }
    });

    const overdueServices = await Client.find({
      nextServiceDate: {
        $lt: today.start
      }
    });

    console.log('Dashboard query results:', {
      todayCount: todayServices.length,
      tomorrowCount: tomorrowServices.length,
      overdueCount: overdueServices.length,
      totalClients: await Client.countDocuments()
    });

    // Test 4: List all clients
    console.log('\n=== Test 4: Listing all clients ===');
    const allClients = await Client.find({}).sort({ nextServiceDate: 1 });
    console.log('All clients:', allClients.map(c => ({
      id: c._id,
      name: c.name,
      serviceType: c.serviceType,
      nextServiceDate: c.nextServiceDate
    })));

    console.log('\n=== Test completed successfully ===');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the test
testDataFlow();
