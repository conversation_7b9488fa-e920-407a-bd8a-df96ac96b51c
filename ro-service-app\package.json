{"name": "ro-service-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@types/mongoose": "^5.11.96", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "mongoose": "^8.18.1", "next": "15.5.3", "node-fetch": "^3.3.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "typescript": "^5"}}